import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:expandable/expandable.dart';
import 'package:swadesic/features/store_faq/store_faq_bloc.dart';
import 'package:swadesic/features/store_faq/add_edit_faq_bottom_sheet.dart';
import 'package:swadesic/features/store_faq/store_faq_navigation.dart';
import 'package:swadesic/model/faq/faq_model.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class StoreFaqScreen extends StatefulWidget {
  final String storeReference;
  final String? storeName;
  final String? initialCategoryKey;
  final String? initialQuestionKey;
  final bool isStoreOwner;

  const StoreFaqScreen({
    Key? key,
    required this.storeReference,
    this.storeName,
    this.initialCategory<PERSON>ey,
    this.initialQuestionKey,
    this.isStoreOwner = false,
  }) : super(key: key);

  @override
  State<StoreFaqScreen> createState() => _StoreFaqScreenState();
}

class _StoreFaqScreenState extends State<StoreFaqScreen> {
  late StoreFaqBloc storeFaqBloc;
  ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    storeFaqBloc = StoreFaqBloc();
    _initializeBloc();
  }

  void _initializeBloc() {
    storeFaqBloc.init(
      storeReference: widget.storeReference,
      initialCategoryKey: widget.initialCategoryKey,
      initialQuestionKey: widget.initialQuestionKey,
      isStoreOwner: widget.isStoreOwner,
    );
  }

  @override
  void dispose() {
    storeFaqBloc.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appWhite,
      appBar: AppBar(
        title: Text(
          widget.storeName != null ? '${widget.storeName} FAQ' : 'Store FAQ',
          style: AppTextStyle.pageHeading(textColor: AppColors.appBlack),
        ),
        backgroundColor: AppColors.appWhite,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: AppColors.appBlack),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.share, color: AppColors.appBlack),
            onPressed: _shareCurrentCategory,
          ),
        ],
      ),
      body: StreamBuilder<StoreFaqScreenState>(
        stream: storeFaqBloc.stateStream,
        initialData: StoreFaqScreenState.Initial,
        builder: (context, snapshot) {
          if (snapshot.data == StoreFaqScreenState.Loading) {
            return const Center(
              child: CircularProgressIndicator(
                color: AppColors.brandBlack,
              ),
            );
          }

          if (snapshot.data == StoreFaqScreenState.Failed) {
            return AppCommonWidgets.errorWidget(
              errorMessage: 'Failed to load store FAQ data',
              onTap: () {
                _initializeBloc();
              },
            );
          }

          if (snapshot.data == StoreFaqScreenState.Success) {
            // Scroll to specific question if deep linking
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _scrollToQuestionIfNeeded();
            });

            return Column(
              children: [
                _buildCategoryTabs(),
                Expanded(
                  child: _buildFaqList(),
                ),
              ],
            );
          }

          return const SizedBox();
        },
      ),
      floatingActionButton: widget.isStoreOwner ? _buildFloatingActionButton() : null,
    );
  }

  Widget _buildCategoryTabs() {
    if (storeFaqBloc.storeFaqDataModel.getStoreFaqCategories.isEmpty) {
      return const SizedBox();
    }

    return Container(
      height: 40,
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: StreamBuilder<int>(
        stream: storeFaqBloc.selectedCategoryStream,
        initialData: storeFaqBloc.selectedCategoryIndex,
        builder: (context, snapshot) {
          return ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: storeFaqBloc.storeFaqDataModel.getStoreFaqCategories.length,
            padding: const EdgeInsets.symmetric(horizontal: 10),
            itemBuilder: (context, index) {
              final isSelected = index == (snapshot.data ?? 0);
              final category = storeFaqBloc.storeFaqDataModel.getStoreFaqCategories[index];
              
              return GestureDetector(
                onTap: () => storeFaqBloc.changeCategory(index),
                child: Container(
                  margin: const EdgeInsets.only(right: 8),
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                  height: 32,
                  decoration: BoxDecoration(
                    color: isSelected ? AppColors.appBlack : AppColors.appWhite,
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: isSelected ? AppColors.appBlack : AppColors.borderColor1,
                    ),
                  ),
                  child: Center(
                    child: Text(
                      category.name,
                      style: AppTextStyle.access0(
                        textColor: isSelected ? AppColors.appWhite : AppColors.appBlack,
                      ),
                    ),
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildFaqList() {
    // Check if categories are loaded
    if (storeFaqBloc.storeFaqDataModel.getStoreFaqCategories.isEmpty) {
      return const Center(
        child: Text('No FAQ data available'),
      );
    }

    if (storeFaqBloc.selectedCategoryIndex >= 
        storeFaqBloc.storeFaqDataModel.getStoreFaqCategories.length) {
      return const Center(
        child: Text('No FAQ data available'),
      );
    }

    final currentCategory = storeFaqBloc
        .storeFaqDataModel.getStoreFaqCategories[storeFaqBloc.selectedCategoryIndex];

    if (currentCategory.items.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'No FAQ items in this category',
              style: AppTextStyle.access0(textColor: AppColors.writingBlack1),
            ),
            if (widget.isStoreOwner) ...[
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _showAddFaqBottomSheet,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.brandBlack,
                  foregroundColor: AppColors.appWhite,
                ),
                child: const Text('Add First FAQ'),
              ),
            ],
          ],
        ),
      );
    }

    return StreamBuilder<int>(
      stream: storeFaqBloc.selectedCategoryStream,
      initialData: storeFaqBloc.selectedCategoryIndex,
      builder: (context, snapshot) {
        final category = storeFaqBloc
            .storeFaqDataModel.getStoreFaqCategories[snapshot.data ?? 0];
        
        if (widget.isStoreOwner) {
          return ReorderableListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: category.items.length,
            onReorder: _onReorder,
            itemBuilder: (context, index) {
              final faqItem = category.items[index];
              return _buildFaqItem(faqItem, index, category.categoryKey);
            },
          );
        } else {
          return ListView.builder(
            controller: _scrollController,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: category.items.length,
            itemBuilder: (context, index) {
              final faqItem = category.items[index];
              return _buildFaqItem(faqItem, index, category.categoryKey);
            },
          );
        }
      },
    );
  }

  Widget _buildFaqItem(StoreFaqItem faqItem, int index, String categoryKey) {
    return Container(
      key: ValueKey('store_faq_item_${faqItem.itemKey}'),
      margin: const EdgeInsets.symmetric(vertical: 4),
      decoration: BoxDecoration(
        color: AppColors.appWhite,
        borderRadius: const BorderRadius.all(Radius.circular(10)),
        border: Border.all(color: AppColors.borderColor1),
      ),
      child: ClipRRect(
        borderRadius: const BorderRadius.all(Radius.circular(10)),
        child: widget.isStoreOwner
            ? Slidable(
                key: ValueKey(faqItem.itemKey),
                endActionPane: ActionPane(
                  motion: const DrawerMotion(),
                  children: [
                    SlidableAction(
                      onPressed: (_) => _editFaqItem(faqItem),
                      backgroundColor: AppColors.brandBlack,
                      foregroundColor: Colors.white,
                      icon: Icons.edit,
                      label: 'Edit',
                    ),
                    SlidableAction(
                      onPressed: (_) => _deleteFaqItem(faqItem),
                      backgroundColor: AppColors.red,
                      foregroundColor: Colors.white,
                      icon: Icons.delete,
                      label: 'Delete',
                    ),
                  ],
                ),
                child: _buildFaqItemContent(faqItem, categoryKey),
              )
            : _buildFaqItemContent(faqItem, categoryKey),
      ),
    );
  }

  Widget _buildFaqItemContent(StoreFaqItem faqItem, String categoryKey) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: ExpandablePanel(
        theme: const ExpandableThemeData(
          animationDuration: Duration(milliseconds: 300),
          iconPlacement: ExpandablePanelIconPlacement.right,
          tapBodyToCollapse: true,
          tapHeaderToExpand: true,
          tapBodyToExpand: true,
          iconPadding: EdgeInsets.zero,
          headerAlignment: ExpandablePanelHeaderAlignment.center,
          iconSize: 24,
          iconColor: AppColors.appBlack,
        ),
        controller: ExpandableController(initialExpanded: faqItem.isExpanded),
        header: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: Row(
            children: [
              if (widget.isStoreOwner)
                Icon(
                  Icons.drag_handle,
                  color: AppColors.writingBlack1,
                  size: 20,
                ),
              if (widget.isStoreOwner) const SizedBox(width: 8),
              Expanded(
                child: Text(
                  faqItem.question,
                  style: AppTextStyle.access0(
                    textColor: AppColors.appBlack,
                  ).copyWith(fontWeight: FontWeight.w600),
                ),
              ),
              IconButton(
                icon: Icon(Icons.share, color: AppColors.writingBlack1, size: 20),
                onPressed: () => _shareQuestion(categoryKey, faqItem),
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
              ),
            ],
          ),
        ),
        collapsed: const SizedBox(),
        expanded: Padding(
          padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Divider(height: 1),
              const SizedBox(height: 12),
              Text(
                faqItem.answer,
                style: AppTextStyle.access0(
                  textColor: AppColors.writingBlack1,
                ).copyWith(height: 1.5),
              ),
              if (faqItem.itemImages.isNotEmpty) ...[
                const SizedBox(height: 12),
                _buildFaqImages(faqItem.itemImages),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFaqImages(List<String> images) {
    return SizedBox(
      height: 100,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: images.length,
        itemBuilder: (context, index) {
          return Container(
            margin: const EdgeInsets.only(right: 8),
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppColors.borderColor1),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                images[index],
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    color: AppColors.textFieldFill1,
                    child: const Icon(
                      Icons.image_not_supported,
                      color: AppColors.writingBlack1,
                    ),
                  );
                },
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    return FloatingActionButton(
      onPressed: _showAddFaqBottomSheet,
      backgroundColor: AppColors.brandBlack,
      child: const Icon(Icons.add, color: AppColors.appWhite),
    );
  }

  // Event handlers
  void _onReorder(int oldIndex, int newIndex) {
    if (!widget.isStoreOwner) return;

    final currentCategory = storeFaqBloc.getCurrentCategory();
    if (currentCategory == null || oldIndex >= currentCategory.items.length) return;

    final item = currentCategory.items[oldIndex];

    // Adjust newIndex if moving down
    if (newIndex > oldIndex) {
      newIndex -= 1;
    }

    // Call API to reorder
    storeFaqBloc.reorderFaqItems(
      itemKey: item.itemKey,
      newOrder: newIndex + 1, // API expects 1-based ordering
    );
  }

  void _showAddFaqBottomSheet() {
    if (!widget.isStoreOwner) return;

    CommonMethods.accessBottomSheet(
      screen: AddEditFaqBottomSheet(
        onSave: (categoryName, question, answer) async {
          final success = await storeFaqBloc.addFaqItem(
            categoryName: categoryName,
            question: question,
            answer: answer,
          );

          if (success) {
            Navigator.pop(context);
            CommonMethods.toastMessage('FAQ item added successfully', context);
          } else {
            CommonMethods.toastMessage('Failed to add FAQ item', context);
          }
        },
      ),
      context: context,
    );
  }

  void _editFaqItem(StoreFaqItem faqItem) {
    if (!widget.isStoreOwner) return;

    final currentCategory = storeFaqBloc.getCurrentCategory();
    if (currentCategory == null) return;

    CommonMethods.accessBottomSheet(
      screen: AddEditFaqBottomSheet(
        isEdit: true,
        initialCategoryName: currentCategory.name,
        initialQuestion: faqItem.question,
        initialAnswer: faqItem.answer,
        onSave: (categoryName, question, answer) async {
          final success = await storeFaqBloc.editFaqItem(
            itemKey: faqItem.itemKey,
            question: question,
            answer: answer,
          );

          if (success) {
            Navigator.pop(context);
            CommonMethods.toastMessage('FAQ item updated successfully', context);
          } else {
            CommonMethods.toastMessage('Failed to update FAQ item', context);
          }
        },
      ),
      context: context,
    );
  }

  void _deleteFaqItem(StoreFaqItem faqItem) {
    if (!widget.isStoreOwner) return;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Delete FAQ Item'),
          content: const Text('Are you sure you want to delete this FAQ item?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () async {
                Navigator.pop(context);
                final success = await storeFaqBloc.deleteFaqItem(faqItem.itemKey);

                if (success) {
                  CommonMethods.toastMessage('FAQ item deleted successfully', context);
                } else {
                  CommonMethods.toastMessage('Failed to delete FAQ item', context);
                }
              },
              child: const Text('Delete', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }

  void _scrollToQuestionIfNeeded() {
    if (widget.initialQuestionKey == null) return;

    // Find the question in the current category
    final currentCategory = storeFaqBloc.getCurrentCategory();
    if (currentCategory == null) return;

    final questionIndex = currentCategory.items.indexWhere(
      (item) => item.itemKey == widget.initialQuestionKey,
    );

    if (questionIndex != -1) {
      // Scroll to the question with animation
      Future.delayed(const Duration(milliseconds: 300), () {
        if (_scrollController.hasClients) {
          _scrollController.animateTo(
            questionIndex * 80.0, // Approximate item height
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeInOut,
          );
        }
      });
    }
  }

  // Share current category
  void _shareCurrentCategory() {
    final currentCategory = storeFaqBloc.getCurrentCategory();
    if (currentCategory == null) return;

    final shareData = StoreFaqNavigation.createShareableLink(
      storeReference: widget.storeReference,
      storeName: widget.storeName ?? 'this store',
      categoryKey: currentCategory.categoryKey,
      categoryName: currentCategory.name,
    );

    // Simple share dialog for now
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Share Category'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(shareData['message']!),
              const SizedBox(height: 16),
              SelectableText(shareData['url']!),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Close'),
            ),
          ],
        );
      },
    );
  }

  // Share specific question
  void _shareQuestion(String categoryKey, StoreFaqItem faqItem) {
    final shareData = StoreFaqNavigation.createShareableLink(
      storeReference: widget.storeReference,
      storeName: widget.storeName ?? 'this store',
      categoryKey: categoryKey,
      questionKey: faqItem.itemKey,
      questionText: faqItem.question,
    );

    // Simple share dialog for now
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Share Question'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(shareData['message']!),
              const SizedBox(height: 16),
              SelectableText(shareData['url']!),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Close'),
            ),
          ],
        );
      },
    );
  }
}
