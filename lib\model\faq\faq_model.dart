class FaqItem {
  final String id; // This will be item_key from API
  final String question;
  final String answer;
  final int order;
  final List<String> itemImages;
  bool isExpanded;

  FaqItem({
    required this.id,
    required this.question,
    required this.answer,
    required this.order,
    this.itemImages = const [],
    this.isExpanded = false,
  });

  // Factory constructor for JSON parsing
  factory FaqItem.fromJson(Map<String, dynamic> json) {
    return FaqItem(
      id: json['item_key'] as String,
      question: json['question'] as String,
      answer: json['answer'] as String,
      order: json['order'] as int,
      itemImages: (json['item_images'] as List<dynamic>?)
              ?.map((image) => image.toString())
              .toList() ??
          [],
      isExpanded: json['isExpanded'] as bool? ?? false,
    );
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'item_key': id,
      'question': question,
      'answer': answer,
      'order': order,
      'item_images': itemImages,
      'isExpanded': isExpanded,
    };
  }
}

// Store FAQ Item - similar to FaqItem but with store_reference
class StoreFaqItem {
  final String itemKey;
  final String question;
  final String answer;
  final int order;
  final List<String> itemImages;
  final String storeReference;
  bool isExpanded;

  StoreFaqItem({
    required this.itemKey,
    required this.question,
    required this.answer,
    required this.order,
    required this.storeReference,
    this.itemImages = const [],
    this.isExpanded = false,
  });

  // Factory constructor for JSON parsing
  factory StoreFaqItem.fromJson(Map<String, dynamic> json) {
    return StoreFaqItem(
      itemKey: json['item_key'] as String,
      question: json['question'] as String,
      answer: json['answer'] as String,
      order: json['order'] as int,
      storeReference: json['store_reference'] as String,
      itemImages: (json['item_images'] as List<dynamic>?)
              ?.map((image) => image.toString())
              .toList() ??
          [],
      isExpanded: json['isExpanded'] as bool? ?? false,
    );
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'item_key': itemKey,
      'question': question,
      'answer': answer,
      'order': order,
      'store_reference': storeReference,
      'item_images': itemImages,
      'isExpanded': isExpanded,
    };
  }

  // Create a copy with updated fields
  StoreFaqItem copyWith({
    String? itemKey,
    String? question,
    String? answer,
    int? order,
    String? storeReference,
    List<String>? itemImages,
    bool? isExpanded,
  }) {
    return StoreFaqItem(
      itemKey: itemKey ?? this.itemKey,
      question: question ?? this.question,
      answer: answer ?? this.answer,
      order: order ?? this.order,
      storeReference: storeReference ?? this.storeReference,
      itemImages: itemImages ?? this.itemImages,
      isExpanded: isExpanded ?? this.isExpanded,
    );
  }
}

class FaqCategory {
  final String id; // This will be category_key from API
  final String name;
  final int order;
  final List<FaqItem> items;

  FaqCategory({
    required this.id,
    required this.name,
    required this.order,
    required this.items,
  });

  // Factory constructor for JSON parsing
  factory FaqCategory.fromJson(Map<String, dynamic> json) {
    return FaqCategory(
      id: json['category_key'] as String,
      name: json['name'] as String,
      order: json['order'] as int,
      items: (json['items'] as List<dynamic>)
          .map((item) => FaqItem.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'category_key': id,
      'name': name,
      'order': order,
      'items': items.map((item) => item.toJson()).toList(),
    };
  }
}

// Store FAQ Category - similar to FaqCategory but with store_reference
class StoreFaqCategory {
  final String categoryKey;
  final String name;
  final int order;
  final String storeReference;
  final List<StoreFaqItem> items;

  StoreFaqCategory({
    required this.categoryKey,
    required this.name,
    required this.order,
    required this.storeReference,
    required this.items,
  });

  // Factory constructor for JSON parsing
  factory StoreFaqCategory.fromJson(Map<String, dynamic> json) {
    return StoreFaqCategory(
      categoryKey: json['category_key'] as String,
      name: json['name'] as String,
      order: json['order'] as int,
      storeReference: json['store_reference'] as String,
      items: (json['items'] as List<dynamic>)
          .map((item) => StoreFaqItem.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'category_key': categoryKey,
      'name': name,
      'order': order,
      'store_reference': storeReference,
      'items': items.map((item) => item.toJson()).toList(),
    };
  }

  // Create a copy with updated fields
  StoreFaqCategory copyWith({
    String? categoryKey,
    String? name,
    int? order,
    String? storeReference,
    List<StoreFaqItem>? items,
  }) {
    return StoreFaqCategory(
      categoryKey: categoryKey ?? this.categoryKey,
      name: name ?? this.name,
      order: order ?? this.order,
      storeReference: storeReference ?? this.storeReference,
      items: items ?? this.items,
    );
  }
}

// FAQ Response wrapper class
class FaqResponse {
  final List<FaqCategory> faqCategories;

  FaqResponse({required this.faqCategories});

  factory FaqResponse.fromJson(Map<String, dynamic> json) {
    return FaqResponse(
      faqCategories: (json['faq_categories'] as List<dynamic>)
          .map((category) =>
              FaqCategory.fromJson(category as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'faq_categories':
          faqCategories.map((category) => category.toJson()).toList(),
    };
  }
}

// Store FAQ Response wrapper class
class StoreFaqResponse {
  final List<StoreFaqCategory> faqCategories;

  StoreFaqResponse({required this.faqCategories});

  factory StoreFaqResponse.fromJson(Map<String, dynamic> json) {
    return StoreFaqResponse(
      faqCategories: (json['faq_categories'] as List<dynamic>)
          .map((category) =>
              StoreFaqCategory.fromJson(category as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'faq_categories':
          faqCategories.map((category) => category.toJson()).toList(),
    };
  }
}
