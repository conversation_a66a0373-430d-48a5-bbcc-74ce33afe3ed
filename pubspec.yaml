name: swadesic
description: A new Flutter project.
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 2.1.4+47

environment:
  sdk: '>=2.20.6 <3.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
#  install_referrer: ^1.2.1
#  android_play_install_referrer: ^0.4.0
  provider: ^6.0.5
#  flutter_screenutil: ^5.7.0
#  base32: ^2.1.3
  flutter_svg: ^1.1.6
  #  rich_text_controller: ^1.4.0
#  image_picker: ^0.8.5+3
  image_picker: ^1.0.4
#  firebase_messaging: ^14.6.3
  flutter_spinkit: ^5.2.0
#  confetti: ^0.7.0
  flutter_image_compress: ^2.0.4
  url_launcher: ^6.2.5
#  firebase_analytics: ^10.4.4
  pin_code_fields: ^7.3.0
  flutter_switch: ^0.3.2
  firebase_core: ^2.24.2
  firebase_messaging: ^14.7.4
#  syncfusion_flutter_sliders: ^23.1.38
  syncfusion_flutter_sliders: ^21.2.6
  #Secure data saved in cache (User info and toke)
  flutter_secure_storage: ^9.0.0
  reorderables: ^0.5.0
  auto_size_text: ^3.0.0
  #Handle root and not verified app
  freerasp: ^6.4.0
  #uni_links: ^0.5.1
  app_links: ^4.0.1
  #Check is keyboard visible or not
  flutter_keyboard_visibility: ^6.0.0
  #Google login to get user email id
  google_sign_in: ^6.2.1
  carousel_slider: ^5.0.0
#  file_picker: ^4.5.1
  file_picker: ^5.3.0
  #Google login adn auth
  firebase_auth: ^4.17.4
  share_plus: ^7.2.1
  cached_network_image: ^3.3.0
#  extended_image: ^6.2.1
#  extended_image: ^8.0.1
#  extended_image: ^8.1.1
  #  hashtagable: ^2.1.0
  #  sliding_up_panel: ^2.0.0+1
  #  uuid: ^3.0.6
#  geolocator: ^9.0.2
  geolocator: ^8.2.1
  geocoding: ^2.2.0
#  geocoding: ^2.0.5
  scrollable_positioned_list: ^0.3.8
  flutter_staggered_grid_view: ^0.4.1
  emoji_picker_flutter: ^1.1.2
  dio: ^4.0.4
  shimmer: ^3.0.0
  flutter_rating_bar: ^4.0.0
  open_file: ^3.3.2
  persistent_bottom_nav_bar: ^5.0.2
  #  modal_bottom_sheet: ^2.1.2
#  device_preview: ^1.1.0
  expandable: ^5.0.1
  #  configurable_expansion_tile_null_safety: ^2.0.3
  photo_view: ^0.14.0
  #  dropdown_search: ^5.0.5
  lottie: ^1.4.0
  webview_flutter: ^3.0.4
  flutter_inappwebview: ^5.8.0
  #Check app update
  in_app_update: ^4.2.2
  #  flutter_slidable: ^2.0.0
  http: ^0.13.6
  #  provider: ^6.0.3
  badges: ^3.0.3
  #  equatable: ^2.0.5
#  firebase_dynamic_links: ^5.0.17
#  firebase_core: ^2.8.0
#  firebase_dynamic_links: ^5.3.3
#  firebase_core: ^2.10.0
  #  cloud_firestore: ^3.4.7
  mask_text_input_formatter: ^2.4.0
  #  intl:
  #  table_calendar: ^2.0.0
  visibility_detector: ^0.3.3
  #  focus_detector: ^2.0.1
  #  bottom_sheet: ^3.1.2
  intl: ^0.18.1
  #  uni_links: ^0.5.1
  permission_handler: ^11.1.0
#  flutter_contacts: ^1.1.5
  #Read mobile contacts
  contacts_service: ^0.6.3
  percent_indicator: ^4.2.2
  rich_text_controller: ^1.4.0
  device_info_plus: ^9.1.1
  #Rooted device detect
  safe_device: ^1.1.4
  # Flutter pdf view
  flutter_pdfview: ^1.3.2
  # Print pdf
#  printing: ^5.9.3
#  printing: ^3.7.2
  pdf: ^3.1.0
  image_cropper: ^3.0.1
  #For open app settings
  app_settings: ^5.1.1
  #For native splash screen
  #  flutter_native_splash: ^2.2.19
#  shared_preferences:
#To get app info and app version
#  package_info_plus: ^1.3.1
  package_info_plus: ^4.0.0
  connectivity_plus: ^4.0.2
  web_socket_channel: ^2.4.0
  fluttertoast: ^8.2.1
  any_link_preview: ^3.0.0
  animated_text_kit: ^4.2.2
  marquee: ^2.2.3
  flutter_markdown: ^0.6.17
  firebase_analytics: ^10.4.4
  flutter_local_notifications: ^16.2.0
  #On slide get option to edit and delete
  flutter_slidable: ^3.0.1
  #Received smd auto fill in text field
  sms_autofill: ^2.3.0
  #Listen all message
#  telephony: ^0.2.0
  # Read url in browser
  universal_html: ^2.2.4
  #Remove # in url
  url_strategy: ^0.2.0
  #Physicall button pressable:
  android_physical_buttons: ^0.1.0
  #Encryption
  encrypt: ^5.0.3
  #Crash analytics
  firebase_crashlytics: ^3.4.8
  #Read more and less
  readmore: ^3.0.0
  #Base 32
  base32: ^2.1.3
  #Get installed upi apps
  upi_india: ^3.0.1
  #Drop down
  dropdown_button2: ^2.3.9
  #Chart
  fl_chart: ^0.68.0
  #Razor pay payment gate way
  razorpay_flutter: ^1.3.7
  #Save data in local db
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  #For signature
  signature: ^5.3.0
  path_provider: ^2.1.4
  flutter_html_to_pdf: ^0.7.0
  #Meta data from url
  metadata_fetch: ^0.4.1
  #Link detection and formatting
  flutter_linkify: ^6.0.0
  #HTML parsing
  html: ^0.15.4
  #Firebase remote config
  firebase_remote_config: ^4.2.4
  #Animate
  flutter_animate: ^4.5.0
  #Get unique hash
  uuid: ^4.5.1
  #Flip
  flip_card: ^0.7.0
  isar: ^3.1.0+1
  #QR Code
  pretty_qr_code: ^3.3.0
  qr: ^3.0.1
  #Image processing
  image: ^3.3.0
  palette_generator: ^0.3.3  # Or latest version


dev_dependencies:
  hive_generator: ^2.0.1
  build_runner: ^2.4.4
  flutter_launcher_icons: ^0.11.0  # Ensure you're using the latest version
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter
#  flutter_driver:
#    sdk: flutter
#  test:

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^2.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/store_shared_link_icons/
    - assets/invalid/
    - assets/transaction_icons/
    - assets/delivery_and_return_default_response/
    - assets/seller/sellers_home/
    - assets/seller/add_product/
    - assets/seller/product_preview/
    - assets/common_images/
    - assets/bottom_navigation_icons/
    - assets/shopping_cart/
    - assets/upi_icons/
    - assets/india_locations/
    - assets/post/
    - assets/home_access_icons/
    - assets/lottie_animation/
    - assets/media/
    - assets/place_holder/
    - assets/maintenance_icons/
    - assets/maintenance_info/
    - assets/mini_dashboard/
    - assets/animations/
    - assets/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: ComfortaaBold
      fonts:
        - asset: fonts/Comfortaa-Bold.ttf
    - family: LavishlyYoursRegular
      fonts:
        - asset: fonts/LavishlyYours-Regular.ttf
    - family: RobotoRegular
      fonts:
        - asset: fonts/Roboto-Regular.ttf
    - family: RobotoMedium
      fonts:
        - asset: fonts/Roboto-Medium.ttf
    - family: RobotoBold
      fonts:
        - asset: fonts/Roboto-Bold.ttf
    - family: cNeueRegular
      fonts:
        - asset: fonts/ComicNeue-Regular.ttf
    - family: LeagueBlack
      fonts:
        - asset: fonts/LeagueSpartan-Black.ttf
    - family: LeagueSemiBold
      fonts:
        - asset: fonts/LeagueSpartan-SemiBold.ttf
    - family: LeagueExtraBold
      fonts:
        - asset: fonts/LeagueSpartan-ExtraBold.ttf
    - family: RobotoMonoBold
      fonts:
        - asset: fonts/RobotoMono-Bold.ttf
    - family: PoppinsSemiBold
      fonts:
        - asset: fonts/Poppins-SemiBold.ttf
    - family: PoppinsBlack
      fonts:
        - asset: fonts/Poppins-Black.ttf
    - family: PoppinsExtraBold
      fonts:
        - asset: fonts/Poppins-ExtraBold.ttf
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages

flutter_icons:
  android: true
  ios: true
  image_path: "assets/common_images/swadesic-new-logo-favicon.png"  # Use your actual icon path
  adaptive_icon_background: "#ffffff"  # Or use an image
  adaptive_icon_foreground: "assets/common_images/swadesic-new-logo-favicon.png"
