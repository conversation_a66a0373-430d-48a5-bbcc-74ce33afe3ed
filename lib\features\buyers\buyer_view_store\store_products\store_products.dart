
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_home/buyer_home_bloc.dart';
import 'package:swadesic/features/buyers/buyer_home/buyer_home_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_store/store_products/store_product_bloc.dart';
import 'package:swadesic/features/buyers/buyer_view_store/store_products/store_product_pagination.dart';
import 'package:swadesic/features/data_model/product_data_model/product_data_model.dart';
import 'package:swadesic/features/data_model/store_dashboard_data_model/store_dashboard_data_model.dart';
import 'package:swadesic/features/seller/seller_store/store_dashboard/complete_check_list/complete_check_list.dart';
import 'package:swadesic/features/seller/seller_store/store_dashboard/store_dashboard_bloc.dart';
import 'package:swadesic/model/buyer_search_response/store_list_response.dart';
import 'package:swadesic/model/store_info/store_info.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_search_field/app_search_field.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:visibility_detector/visibility_detector.dart';

class StoreProducts extends StatefulWidget {
  final String storeReference;
  final StoreInfo storeInfo;

  const StoreProducts({
    Key? key,
    required this.storeReference,
    required this.storeInfo,
  }) : super(key: key);

  @override
  StoreProductsState createState() => StoreProductsState();
}

class StoreProductsState extends State<StoreProducts>
    with AutomaticKeepAliveClientMixin<StoreProducts> {
  //Keep alive
  @override
  bool get wantKeepAlive => true;

  //Width
  double width = 0.0;

  //region Bloc
  late StoreProductBloc storeProductBloc;

  //endregion

  //region Init
  @override
  void initState() {
    storeProductBloc =
        StoreProductBloc(context, widget.storeReference, widget.storeInfo);
    storeProductBloc.init();

    // Listen for global product refresh notifications
    _setupProductRefreshListener();

    super.initState();
  }

  //endregion

  //region Setup Product Refresh Listener
  void _setupProductRefreshListener() {
    AppConstants.productRefreshCtrl.stream.listen((data) {
      // Check if the refresh is for this store
      if (data['storeReference'] == widget.storeReference) {
        // Refresh products for this store
        storeProductBloc.refreshStoreProducts();
      }
    });
  }
  //endregion

  //region Did update
  @override
  void didUpdateWidget(covariant StoreProducts oldWidget) {
    //updateData is false then do nothing else update

    // if (widget.updateData != oldWidget.updateData) {
    //   //print("updateData has changed: ${widget.updateData}");
    //   // Perform any other actions you need to do with the updated data here
    // }
    // //print(widget.updateData);
    // // widget.updateData?storeProductBloc.getStoreProducts():null;
    super.didUpdateWidget(oldWidget);
  }

  //endregion

  //region Dispose
  @override
  void dispose() {
    storeProductBloc.dispose();
    super.dispose();
  }

  //endregion

  //region Public refresh method
  Future<void> refreshProducts() async {
    await storeProductBloc.refreshStoreProducts();
  }
  //endregion

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin
    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        width = constraints.maxWidth;
        return GestureDetector(
          onTap: () {
            CommonMethods.closeKeyboard(context);
          },
          child: body(),
        );
      },
    );
  }

  Widget body() {
    return Column(
      children: [
        // Search Bar
        Visibility(
          visible: storeProductBloc.storeProductList.isNotEmpty,
          child: Container(
            margin:
                const EdgeInsets.only(left: 16, right: 16, top: 5, bottom: 10),
            // padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
            child: AppSearchField(
              textEditingController: storeProductBloc.searchCtrl,
              onChangeText: storeProductBloc.onSearchProducts,
              onSubmit: () => storeProductBloc
                  .onSearchProducts(storeProductBloc.searchCtrl.text),
              onTapSuffix: () => storeProductBloc.onSearchProducts(""),
              hintText: "Search products",
              isAutoFocus: false,
            ),
          ),
        ),
        // Product Grid
        Expanded(
          child: RefreshIndicator(
            color: AppColors.brandBlack,
            onRefresh: () async {
              await storeProductBloc.refreshStoreProducts();
            },
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: StreamBuilder<StoreProductListState>(
                stream: storeProductBloc.storeProductsCtrl.stream,
                initialData: StoreProductListState.Loading,
                builder: (context, snapshot) {
                  switch (snapshot.data) {
                    case StoreProductListState.Loading:
                      return Container(
                        margin: const EdgeInsets.symmetric(vertical: 100),
                        child: AppCommonWidgets.appCircularProgress(),
                      );
                    case StoreProductListState.Empty:
                      return Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 50),
                        child: Center(
                          child: Text(
                            'Products will be added soon',
                            style: AppTextStyle.contentText0(
                                textColor: AppColors.writingBlack1),
                          ),
                        ),
                      );
                    case StoreProductListState.Failed:
                      return Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 50),
                        child: Center(
                          child: Text(
                            'Failed to load products',
                            style: AppTextStyle.contentText0(
                                textColor: AppColors.writingBlack1),
                          ),
                        ),
                      );
                    case StoreProductListState.Success:
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 16),
                        child: Column(
                          children: [
                            productList(
                                productList: storeProductBloc.storeProductList),
                            // Only show pagination loading if not in search mode
                            if (!storeProductBloc.isSearchActive)
                              paginationLoading(),
                          ],
                        ),
                      );
                    default:
                      return const SizedBox();
                  }
                },
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget productList({required List<Product> productList}) {
    return Consumer<ProductDataModel>(
      builder: (BuildContext context, ProductDataModel productDataModel,
          Widget? child) {
        //Take out the products which are present in Store product bloc

        // For debugging, let's use the productList directly first
        List<Product> filteredProductList = productList;

        // Original filtering logic (commented out for debugging)
        // List<Product> filteredProductList = [];
        // // Create a map for quick lookup based on productReference
        // // Map<String, Product> productMap = {for (var product in productDataModel.allProducts) product.productReference!: product};
        // Map<String, Product> productMap = {
        //   for (var product in productDataModel.allProducts)
        //     if (product.productReference != null)
        //       product.productReference!: product
        // };
        // // Filter and sort based on storeProductBloc.storeProductList order
        // for (var storeProduct in storeProductBloc.storeProductList) {
        //   if (productMap.containsKey(storeProduct.productReference)) {
        //     filteredProductList.add(productMap[storeProduct.productReference]!);
        //   }
        // }

        print("Rendering ${filteredProductList.length} products in grid");

        return Stack(
          children: [
            Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ///Warning about store is not accepting order
                Visibility(
                    visible: !widget.storeInfo.configReceiveOrders! &&
                        AppConstants.appData.isUserView!,
                    child: Container(
                      alignment: Alignment.centerLeft,
                      // height: CommonMethods.textHeight(context: context, textStyle:AppTextStyle.contentHeading0(textColor: AppColors.appWhite), ) *2,
                      // margin: const EdgeInsets.only(top: kToolbarHeight),
                      padding: const EdgeInsets.symmetric(
                          vertical: 10, horizontal: 10),
                      decoration: BoxDecoration(
                          color: AppColors.brandBlack,
                          boxShadow: AppColors.toastMessageShadow),
                      width: double.infinity,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(
                            AppStrings.thisStoreIsNotreceivingOrder,
                            textAlign: TextAlign.center,
                            style: AppTextStyle.contentHeading0(
                                textColor: AppColors.appWhite),
                          ),
                          // Text(
                          //   "know more",
                          //   textAlign: TextAlign.start,
                          //   style: AppTextStyle.contentHeading0(isUnderline: true, textColor: AppColors.appWhite),
                          // ),
                        ],
                      ),
                    )),
                //If warning visible
                Visibility(
                    visible: storeProductBloc.isWarningVisible,
                    child: Container(
                      margin: const EdgeInsets.symmetric(
                          horizontal: 20, vertical: 100),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(
                            AppStrings.shopWithCare,
                            style: AppTextStyle.settingHeading1(
                                textColor: AppColors.writingBlack0),
                          ),
                          verticalSizedBox(5),
                          Text(
                            AppStrings.thisStoreIsNotYetPublic,
                            textAlign: TextAlign.center,
                            style: AppTextStyle.contentText0(
                                textColor: AppColors.writingBlack1),
                          ),
                          verticalSizedBox(20),
                          CupertinoButton(
                              color: AppColors.textFieldFill0,
                              borderRadius: BorderRadius.circular(100),
                              child: Text(
                                AppStrings.showAnyways,
                                style: AppTextStyle.contentText0(
                                    textColor: AppColors.appWhite),
                              ),
                              onPressed: () {
                                storeProductBloc.onTapAnyways();
                              })
                        ],
                      ),
                    )),

                ///Warning visible false
                Visibility(
                    visible: !storeProductBloc.isWarningVisible,
                    child: Column(
                      children: [
                        GridView.builder(
                            addAutomaticKeepAlives: false,
                            padding: const EdgeInsets.only(bottom: 20),
                            addRepaintBoundaries: false,
                            physics: const NeverScrollableScrollPhysics(),
                            shrinkWrap: true,
                            itemCount: filteredProductList.length,
                            // itemCount: 6,
                            gridDelegate:
                                SliverGridDelegateWithFixedCrossAxisCount(
                              // childAspectRatio: MediaQuery.of(context).size.width / (MediaQuery.of(context).size.height / 1.6),
                              crossAxisCount: 2,
                              mainAxisSpacing: 0,
                              crossAxisSpacing: 0,
                              mainAxisExtent: (width / 2) +
                                  CommonMethods.textHeight(
                                      context: context,
                                      textStyle: AppTextStyle.contentHeading0(
                                          textColor: AppColors.appBlack)) +
                                  CommonMethods.textHeight(
                                      context: context,
                                      textStyle: AppTextStyle.contentHeading0(
                                          textColor: AppColors.appBlack)) +
                                  CommonMethods.textHeight(
                                      context: context,
                                      textStyle: AppTextStyle.access0(
                                          textColor: AppColors.appBlack)) +
                                  5,
                            ),
                            itemBuilder: (context, index) {
                              return InkWell(
                                onTap: () {
                                  storeProductBloc
                                      .goToProductListViewScreen(index);
                                  // widget.buyerViewStoreBloc.goToViewProductScreen(index);
                                },
                                child: Stack(
                                  alignment: Alignment
                                      .center, // Aligns children at the center of the Stack

                                  children: [
                                    Opacity(
                                      opacity:
                                          filteredProductList[index].inStock ==
                                                  0
                                              ? 1.0
                                              : 1.0,
                                      child: Container(
                                          padding: EdgeInsets.zero,
                                          decoration: BoxDecoration(
                                            borderRadius:
                                                const BorderRadius.only(
                                                    topLeft:
                                                        Radius.circular(10),
                                                    topRight:
                                                        Radius.circular(10)),
                                            color: AppColors.appWhite,
                                            border: Border.all(
                                                color: AppColors.lightestGrey2),
                                            boxShadow: [
                                              BoxShadow(
                                                offset: const Offset(0, 1),
                                                blurRadius: 4,
                                                color: AppColors.appBlack
                                                    .withOpacity(0.1),
                                              ),
                                            ],
                                          ),
                                          child: Stack(
                                            alignment: Alignment.center,
                                            children: [
                                              ///Product card
                                              AppCommonWidgets
                                                  .productCardInGrid(
                                                // productImage:
                                                //     filteredProductList[index]
                                                //             .prodImages!
                                                //             .isEmpty
                                                //         ? null
                                                //         : filteredProductList[
                                                //                 index]
                                                //             .prodImages![0]
                                                //             .productImage,
                                                // productBrand:
                                                //     filteredProductList[index]
                                                //         .brandName!,
                                                // productName:
                                                //     filteredProductList[index]
                                                //         .productName!,
                                                // sellingPrice:
                                                //     filteredProductList[index]
                                                //         .sellingPrice!
                                                //         .toString(),
                                                // mrp: filteredProductList[index]
                                                //     .mrpPrice!
                                                //     .toString(),
                                                context: context,
                                                screenWidth: width,
                                                product: filteredProductList[index],
                                              ),
                                            ],
                                          )),
                                    ),

                                    // Out of stock badge positioned at bottom right
                                    // if (filteredProductList[index].inStock == 0)
                                    //   Positioned(
                                    //     bottom: 8,
                                    //     right: 8,
                                    //     child: Container(
                                    //       padding: const EdgeInsets.symmetric(
                                    //           vertical: 4, horizontal: 8),
                                    //       decoration: BoxDecoration(
                                    //         color: AppColors.red.withOpacity(0.9),
                                    //         borderRadius: BorderRadius.circular(4),
                                    //       ),
                                    //       child: Text(
                                    //         AppStrings.outOfStock,
                                    //         style: AppTextStyle.smallText(
                                    //           textColor: AppColors.appWhite,
                                    //           // fontWeight: FontWeight.w500,
                                    //         ),
                                    //       ),
                                    //     ),
                                    //   )

                                    ///Shadow
                                    // storeProductBloc.storeProductResponse.data![index].inStock == 0
                                    //     ? Container(
                                    //   padding: EdgeInsets.zero,
                                    //   decoration: BoxDecoration(
                                    //     borderRadius: const BorderRadius.only(topLeft: Radius.circular(10), topRight: Radius.circular(10)),
                                    //     color: AppColors.appWhite.withOpacity(0.2),
                                    //     border: Border.all(color: AppColors.lightGray2),
                                    //     boxShadow: [
                                    //       BoxShadow(
                                    //         offset: const Offset(0, 1),
                                    //         blurRadius: 5,
                                    //         color: AppColors.appBlack.withOpacity(0.2),
                                    //       ),
                                    //     ],
                                    //   ),
                                    // )
                                    //     : const SizedBox()
                                  ],
                                ),
                              );
                            }),
                            // Container(
                            //   height: 50,
                            //   width: double.infinity,
                            // )
                      ],
                    )),
              ],
            ),
          ],
        );
      },
    );
  }

  //region Pagination loading
  Widget paginationLoading() {
    return Visibility(
      visible: !storeProductBloc
          .isSearchActive, // Hide pagination loading during search
      child: VisibilityDetector(
        key: const Key("pagination_loading"),
        onVisibilityChanged: (info) {
          print("Pagination visibility changed: ${info.visibleFraction}");
          if (info.visibleFraction > 0) {
            print("Triggering pagination load...");
            storeProductBloc.storeProductPagination
                .onPaginationLoadingVisible();
          }
        },
        child: StreamBuilder<StoreProductsPaginationState>(
          stream: storeProductBloc
              .storeProductPagination.storeProductPaginationCtrl.stream,
          initialData: StoreProductsPaginationState.Done,
          builder: (context, snapshot) {
            if (snapshot.data == StoreProductsPaginationState.Loading) {
              return Container(
                margin: const EdgeInsets.symmetric(vertical: 20),
                child: AppCommonWidgets.appCircularProgress(),
              );
            }
            // Always show a small container to ensure the detector has something to detect
            return Container(
              height: 50,
              width: double.infinity,
            );
          },
        ),
      ),
    );
  }
  //endregion
}
