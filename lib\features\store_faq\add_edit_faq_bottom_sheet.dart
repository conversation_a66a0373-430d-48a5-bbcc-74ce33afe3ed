import 'package:flutter/material.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_title_and_options/app_title_and_options.dart';
import 'package:swadesic/util/common_widgets.dart';

class AddEditFaqBottomSheet extends StatefulWidget {
  final bool isEdit;
  final String? initialCategoryName;
  final String? initialQuestion;
  final String? initialAnswer;
  final Function(String categoryName, String question, String answer) onSave;

  const AddEditFaqBottomSheet({
    Key? key,
    this.isEdit = false,
    this.initialCategoryName,
    this.initialQuestion,
    this.initialAnswer,
    required this.onSave,
  }) : super(key: key);

  @override
  State<AddEditFaqBottomSheet> createState() => _AddEditFaqBottomSheetState();
}

class _AddEditFaqBottomSheetState extends State<AddEditFaqBottomSheet> {
  late TextEditingController _categoryController;
  late TextEditingController _questionController;
  late TextEditingController _answerController;
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    _categoryController = TextEditingController(text: widget.initialCategoryName ?? '');
    _questionController = TextEditingController(text: widget.initialQuestion ?? '');
    _answerController = TextEditingController(text: widget.initialAnswer ?? '');
  }

  @override
  void dispose() {
    _categoryController.dispose();
    _questionController.dispose();
    _answerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        left: 20,
        right: 20,
        top: 20,
        bottom: MediaQuery.of(context).viewInsets.bottom + 20,
      ),
      decoration: const BoxDecoration(
        color: AppColors.appWhite,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Center(
              child: Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: AppColors.borderColor1,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            ),
            verticalSizedBox(20),

            // Title using AppTitleAndOptions
            AppTitleAndOptions(
              title: widget.isEdit ? 'Edit FAQ' : 'Add an Option',
              titlePaddingHorizontal: 0,
            ),
            verticalSizedBox(24),

            // Category Name Field (only for new FAQ)
            if (!widget.isEdit) ...[
              Text(
                'Category',
                style: AppTextStyle.access0(textColor: AppColors.appBlack),
              ),
              const SizedBox(height: 8),
              TextFormField(
                controller: _categoryController,
                decoration: InputDecoration(
                  hintText: 'Enter category name',
                  hintStyle: AppTextStyle.access0(textColor: AppColors.writingBlack1),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: AppColors.brandBlack),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: AppColors.brandBlack),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: AppColors.brandBlack, width: 2),
                  ),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter a category name';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
            ],

            // Question Field
            Text(
              'Question',
              style: AppTextStyle.access0(textColor: AppColors.appBlack),
            ),
            const SizedBox(height: 8),
            TextFormField(
              controller: _questionController,
              decoration: InputDecoration(
                hintText: widget.initialQuestion ?? 'Brand name',
                hintStyle: AppTextStyle.access0(textColor: AppColors.writingBlack1),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: AppColors.brandBlack),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: AppColors.brandBlack),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: AppColors.brandBlack, width: 2),
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter a question';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // Answer Field
            Text(
              'Answer',
              style: AppTextStyle.access0(textColor: AppColors.appBlack),
            ),
            const SizedBox(height: 8),
            TextFormField(
              controller: _answerController,
              maxLines: 5,
              decoration: InputDecoration(
                hintText: 'Enter option value',
                hintStyle: AppTextStyle.access0(textColor: AppColors.writingBlack1),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: AppColors.brandBlack),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: AppColors.brandBlack),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: AppColors.brandBlack, width: 2),
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter an answer';
                }
                return null;
              },
            ),
            const SizedBox(height: 24),

            // Add Button
            SizedBox(
              width: double.infinity,
              height: 50,
              child: ElevatedButton(
                onPressed: _handleSave,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.appBlack,
                  foregroundColor: AppColors.appWhite,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  widget.isEdit ? 'Update' : 'Add',
                  style: AppTextStyle.access0(textColor: AppColors.appWhite),
                ),
              ),
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  void _handleSave() {
    if (_formKey.currentState?.validate() ?? false) {
      final categoryName = widget.isEdit 
          ? (widget.initialCategoryName ?? 'General')
          : _categoryController.text.trim();
      final question = _questionController.text.trim();
      final answer = _answerController.text.trim();

      widget.onSave(categoryName, question, answer);
    }
  }
}
