import 'dart:convert';
import 'dart:io';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:swadesic/model/faq/faq_model.dart';
import 'package:swadesic/services/http_service.dart';
import 'package:swadesic/util/app_constants.dart';

class StoreFaqService {
  late HttpService httpService;
  
  // Cache keys
  static const String _cacheKey = 'store_faq_cache';
  static const String _cacheTimestampKey = 'store_faq_cache_timestamp';
  static const int _cacheValidityHours = 24; // Cache valid for 24 hours

  StoreFaqService() {
    httpService = HttpService();
  }

  // region Get Store FAQ Data
  Future<StoreFaqResponse> getStoreFaqData({required String storeReference}) async {
    try {
      // Try to get cached data first
      final cachedData = await _getCachedFaqData(storeReference);
      if (cachedData != null) {
        return cachedData;
      }

      // If no cache or expired, fetch from API
      final response = await _fetchStoreFaqDataFromApi(storeReference);
      
      // Cache the response
      await _cacheStoreFaqData(storeReference, response);
      
      return response;
    } catch (e) {
      // If API fails, try to return cached data even if expired
      final cachedData = await _getCachedFaqData(storeReference, ignoreExpiry: true);
      if (cachedData != null) {
        return cachedData;
      }
      
      // If no cache available, return empty response
      return StoreFaqResponse(faqCategories: []);
    }
  }
  // endregion

  // region Fetch Store FAQ Data from API
  Future<StoreFaqResponse> _fetchStoreFaqDataFromApi(String storeReference) async {
    final url = '${AppConstants.storeFaqData}?store_reference=$storeReference';
    Map<String, dynamic> response = await httpService.getApiCall(url);
    return StoreFaqResponse.fromJson(response);
  }
  // endregion

  // region Cache Store FAQ Data
  Future<void> _cacheStoreFaqData(String storeReference, StoreFaqResponse faqResponse) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = '${_cacheKey}_$storeReference';
      final timestampKey = '${_cacheTimestampKey}_$storeReference';
      
      final jsonString = json.encode(faqResponse.toJson());
      final timestamp = DateTime.now().millisecondsSinceEpoch;

      await prefs.setString(cacheKey, jsonString);
      await prefs.setInt(timestampKey, timestamp);
    } catch (e) {
      // Cache failure shouldn't break the app
      print('Failed to cache store FAQ data: $e');
    }
  }
  // endregion

  // region Get Cached Store FAQ Data
  Future<StoreFaqResponse?> _getCachedFaqData(String storeReference, {bool ignoreExpiry = false}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = '${_cacheKey}_$storeReference';
      final timestampKey = '${_cacheTimestampKey}_$storeReference';
      
      final cachedJson = prefs.getString(cacheKey);
      final timestamp = prefs.getInt(timestampKey);

      if (cachedJson == null || timestamp == null) {
        return null;
      }

      if (!ignoreExpiry) {
        final cacheAge = DateTime.now().millisecondsSinceEpoch - timestamp;
        final maxAge = _cacheValidityHours * 60 * 60 * 1000; // Convert to milliseconds
        
        if (cacheAge > maxAge) {
          return null; // Cache expired
        }
      }

      final jsonData = json.decode(cachedJson);
      return StoreFaqResponse.fromJson(jsonData);
    } catch (e) {
      print('Failed to get cached store FAQ data: $e');
      return null;
    }
  }
  // endregion

  // region Create FAQ Item
  Future<StoreFaqItem> createFaqItem({
    required String storeReference,
    required String categoryName,
    required String question,
    required String answer,
    bool isActive = true,
  }) async {
    final body = {
      'store_reference': storeReference,
      'category_name': categoryName,
      'question': question,
      'answer': answer,
      'is_active': isActive,
    };

    Map<String, dynamic> response = await httpService.postApiCall(body, AppConstants.storeFaqCreateItem);
    
    // Clear cache after creating new item
    await _clearCache(storeReference);
    
    return StoreFaqItem.fromJson(response['data']);
  }
  // endregion

  // region Reorder FAQ Item
  Future<void> reorderFaqItem({
    required String storeReference,
    required String itemKey,
    required int newOrder,
  }) async {
    final url = '${AppConstants.storeFaqReorderItem}?store_reference=$storeReference';
    final body = {
      'item_key': itemKey,
      'new_order': newOrder,
    };

    await httpService.postApiCall(body, url);
    
    // Clear cache after reordering
    await _clearCache(storeReference);
  }
  // endregion

  // region Switch FAQ Items
  Future<void> switchFaqItems({
    required String storeReference,
    required String itemKey1,
    required String itemKey2,
  }) async {
    final url = '${AppConstants.storeFaqSwitchItems}?store_reference=$storeReference';
    final body = {
      'item_key_1': itemKey1,
      'item_key_2': itemKey2,
    };

    await httpService.postApiCall(body, url);
    
    // Clear cache after switching
    await _clearCache(storeReference);
  }
  // endregion

  // region Upload FAQ Item Image
  Future<Map<String, dynamic>> uploadFaqItemImage({
    required String storeReference,
    required String itemKey,
    required File imageFile,
    int? order,
  }) async {
    final url = '${AppConstants.storeFaqUploadImage}?store_reference=$storeReference';
    
    Map<String, dynamic> response = await httpService.uploadFile(
      url: url,
      file: imageFile,
      fieldName: 'image',
      additionalFields: {
        'item_key': itemKey,
        if (order != null) 'order': order.toString(),
      },
    );
    
    // Clear cache after uploading image
    await _clearCache(storeReference);
    
    return response;
  }
  // endregion

  // region Delete FAQ Item Image
  Future<void> deleteFaqItemImage({
    required String storeReference,
    required int imageId,
  }) async {
    final url = '${AppConstants.storeFaqDeleteImage}?image_id=$imageId&store_reference=$storeReference';
    
    await httpService.deleteApiCall(url);
    
    // Clear cache after deleting image
    await _clearCache(storeReference);
  }
  // endregion

  // region Clear Cache
  Future<void> _clearCache(String storeReference) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = '${_cacheKey}_$storeReference';
      final timestampKey = '${_cacheTimestampKey}_$storeReference';
      
      await prefs.remove(cacheKey);
      await prefs.remove(timestampKey);
    } catch (e) {
      print('Failed to clear store FAQ cache: $e');
    }
  }
  // endregion

  // region Clear All Cache
  Future<void> clearAllCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();
      
      for (String key in keys) {
        if (key.startsWith(_cacheKey) || key.startsWith(_cacheTimestampKey)) {
          await prefs.remove(key);
        }
      }
    } catch (e) {
      print('Failed to clear all store FAQ cache: $e');
    }
  }
  // endregion
}
