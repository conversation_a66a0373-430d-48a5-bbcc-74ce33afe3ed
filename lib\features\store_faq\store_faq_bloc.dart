import 'dart:async';
import 'package:flutter/material.dart';
import 'package:swadesic/features/data_model/store_faq_data_model/store_faq_data_model.dart';
import 'package:swadesic/model/faq/faq_model.dart';
import 'package:swadesic/util/app_constants.dart';

enum StoreFaqScreenState {
  Initial,
  Loading,
  Success,
  Failed,
}

class StoreFaqBloc {
  // Stream controllers
  final StreamController<StoreFaqScreenState> _stateController = StreamController<StoreFaqScreenState>.broadcast();
  final StreamController<int> _selectedCategoryController = StreamController<int>.broadcast();

  // Data model
  late StoreFaqDataModel storeFaqDataModel;

  // State variables
  int selectedCategoryIndex = 0;
  String? storeReference;
  String? initialCategoryKey;
  String? initialQuestionKey;
  bool isStoreOwner = false;

  // Getters
  Stream<StoreFaqScreenState> get stateStream => _stateController.stream;
  Stream<int> get selectedCategoryStream => _selectedCategoryController.stream;

  // Constructor
  StoreFaqBloc() {
    storeFaqDataModel = StoreFaqDataModel();
  }

  // Initialize with store reference
  Future<void> init({
    required String storeReference,
    String? initialCategoryKey,
    String? initialQuestionKey,
    bool isStoreOwner = false,
  }) async {
    this.storeReference = storeReference;
    this.initialCategoryKey = initialCategoryKey;
    this.initialQuestionKey = initialQuestionKey;
    this.isStoreOwner = isStoreOwner;

    _stateController.add(StoreFaqScreenState.Loading);

    try {
      await storeFaqDataModel.loadStoreFaqData(storeReference: storeReference);

      if (storeFaqDataModel.getStoreFaqCategories.isNotEmpty) {
        // Set initial category if provided
        if (initialCategoryKey != null) {
          final categoryIndex = storeFaqDataModel.getStoreFaqCategories
              .indexWhere((cat) => cat.categoryKey == initialCategoryKey);
          if (categoryIndex != -1) {
            selectedCategoryIndex = categoryIndex;
          }
        }

        // Expand initial question if provided
        if (initialQuestionKey != null && initialCategoryKey != null) {
          storeFaqDataModel.updateFaqItemLocally(
            categoryKey: initialCategoryKey!,
            itemKey: initialQuestionKey!,
            isExpanded: true,
          );
        }

        _stateController.add(StoreFaqScreenState.Success);
        _selectedCategoryController.add(selectedCategoryIndex);
      } else {
        _stateController.add(StoreFaqScreenState.Success);
      }
    } catch (e) {
      _stateController.add(StoreFaqScreenState.Failed);
    }
  }

  // Change selected category
  void changeCategory(int index) {
    if (index >= 0 && index < storeFaqDataModel.getStoreFaqCategories.length) {
      selectedCategoryIndex = index;
      _selectedCategoryController.add(selectedCategoryIndex);
    }
  }

  // Toggle FAQ item expansion
  void toggleFaqItem(String categoryKey, String itemKey) {
    storeFaqDataModel.toggleFaqItemExpansion(
      categoryKey: categoryKey,
      itemKey: itemKey,
    );
  }

  // Add new FAQ item
  Future<bool> addFaqItem({
    required String categoryName,
    required String question,
    required String answer,
  }) async {
    if (!isStoreOwner) return false;

    _stateController.add(StoreFaqScreenState.Loading);

    try {
      final newItem = await storeFaqDataModel.addFaqItem(
        categoryName: categoryName,
        question: question,
        answer: answer,
      );

      if (newItem != null) {
        _stateController.add(StoreFaqScreenState.Success);
        return true;
      } else {
        _stateController.add(StoreFaqScreenState.Failed);
        return false;
      }
    } catch (e) {
      _stateController.add(StoreFaqScreenState.Failed);
      return false;
    }
  }

  // Edit FAQ item (placeholder - would need API endpoint)
  Future<bool> editFaqItem({
    required String itemKey,
    required String question,
    required String answer,
  }) async {
    if (!isStoreOwner) return false;

    // For now, just update locally
    // In a real implementation, you'd call an API endpoint
    // and then refresh the data
    
    // Find the category containing this item
    String? categoryKey;
    for (final category in storeFaqDataModel.getStoreFaqCategories) {
      if (category.items.any((item) => item.itemKey == itemKey)) {
        categoryKey = category.categoryKey;
        break;
      }
    }

    if (categoryKey != null) {
      storeFaqDataModel.updateFaqItemLocally(
        categoryKey: categoryKey,
        itemKey: itemKey,
        question: question,
        answer: answer,
      );
      return true;
    }

    return false;
  }

  // Delete FAQ item (placeholder - would need API endpoint)
  Future<bool> deleteFaqItem(String itemKey) async {
    if (!isStoreOwner) return false;

    // Placeholder implementation
    // In a real implementation, you'd call an API endpoint
    // and then refresh the data
    
    await refreshData();
    return true;
  }

  // Reorder FAQ items
  Future<bool> reorderFaqItems({
    required String itemKey,
    required int newOrder,
  }) async {
    if (!isStoreOwner) return false;

    _stateController.add(StoreFaqScreenState.Loading);

    try {
      final success = await storeFaqDataModel.reorderFaqItem(
        itemKey: itemKey,
        newOrder: newOrder,
      );

      if (success) {
        _stateController.add(StoreFaqScreenState.Success);
        return true;
      } else {
        _stateController.add(StoreFaqScreenState.Failed);
        return false;
      }
    } catch (e) {
      _stateController.add(StoreFaqScreenState.Failed);
      return false;
    }
  }

  // Switch FAQ items positions
  Future<bool> switchFaqItems({
    required String itemKey1,
    required String itemKey2,
  }) async {
    if (!isStoreOwner) return false;

    _stateController.add(StoreFaqScreenState.Loading);

    try {
      final success = await storeFaqDataModel.switchFaqItems(
        itemKey1: itemKey1,
        itemKey2: itemKey2,
      );

      if (success) {
        _stateController.add(StoreFaqScreenState.Success);
        return true;
      } else {
        _stateController.add(StoreFaqScreenState.Failed);
        return false;
      }
    } catch (e) {
      _stateController.add(StoreFaqScreenState.Failed);
      return false;
    }
  }

  // Refresh data
  Future<void> refreshData() async {
    if (storeReference != null) {
      _stateController.add(StoreFaqScreenState.Loading);
      
      try {
        await storeFaqDataModel.refreshStoreFaqData();
        _stateController.add(StoreFaqScreenState.Success);
      } catch (e) {
        _stateController.add(StoreFaqScreenState.Failed);
      }
    }
  }

  // Get current category
  StoreFaqCategory? getCurrentCategory() {
    if (selectedCategoryIndex >= 0 && 
        selectedCategoryIndex < storeFaqDataModel.getStoreFaqCategories.length) {
      return storeFaqDataModel.getStoreFaqCategories[selectedCategoryIndex];
    }
    return null;
  }

  // Get FAQ item by key in current category
  StoreFaqItem? getFaqItemByKey(String itemKey) {
    final currentCategory = getCurrentCategory();
    if (currentCategory == null) return null;

    try {
      return currentCategory.items.firstWhere((item) => item.itemKey == itemKey);
    } catch (e) {
      return null;
    }
  }

  // Check if category exists
  bool categoryExists(String categoryKey) {
    return storeFaqDataModel.getCategoryByKey(categoryKey) != null;
  }

  // Check if item exists
  bool itemExists(String categoryKey, String itemKey) {
    return storeFaqDataModel.getItemByKeys(categoryKey, itemKey) != null;
  }

  // Dispose
  void dispose() {
    _stateController.close();
    _selectedCategoryController.close();
  }
}
