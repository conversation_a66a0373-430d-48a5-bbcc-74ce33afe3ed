# FAQ API Documentation

Base URL: `/store/faq/`

## Authentication
All endpoints require Bearer token authentication:
```
Authorization: Bearer your-auth-token
```

## 1. Get All FAQ Data
**Endpoint**: `GET /data/`  
**Description**: Retrieves all active FAQ categories with their items for a specific store.

### Query Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| store_reference | string | Yes | Reference ID of the store |

### Example Request
```bash
curl -X GET "http://your-domain.com/store/faq/data/?store_reference=store123" \
  -H "Authorization: Bearer your-auth-token"
```

### Success Response (200 OK)
```json
{
    "faq_categories": [
        {
            "category_key": "general",
            "name": "General",
            "order": 0,
            "items": [
                {
                    "item_key": "What_are_your_55081",
                    "question": "What are your shipping options?",
                    "answer": "We offer standard and express shipping options.",
                    "order": 1,
                    "item_images": [],
                    "store_reference": "S1744452089786"
                },
                {
                    "item_key": "What_are_your_60371",
                    "question": "What are your shipping options?",
                    "answer": "We offer standard and express shipping options.",
                    "order": 2,
                    "item_images": [],
                    "store_reference": "S1744452089786"
                },
                {
                    "item_key": "What_are_your_50301",
                    "question": "What are your shipping options?",
                    "answer": "We offer standard and express shipping options.",
                    "order": 3,
                    "item_images": [],
                    "store_reference": "S1744452089786"
                },
                {
                    "item_key": "What_are_your_68738",
                    "question": "What are your shipping options?",
                    "answer": "We offer standard and express shipping options.",
                    "order": 4,
                    "item_images": [],
                    "store_reference": "S1744452089786"
                },
                {
                    "item_key": "What_are_your_42725",
                    "question": "What are your shipping options?",
                    "answer": "We offer standard and express shipping options.",
                    "order": 5,
                    "item_images": [],
                    "store_reference": "S1744452089786"
                }
            ],
            "store_reference": "S1744452089786"
        }
    ]
}
```

## 2. Upload FAQ Item Image
**Endpoint**: `POST /images/`  
**Description**: Uploads an image for a specific FAQ item.

### Query Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| store_reference | string | Yes | Reference ID of the store |

### Form Data
| Field | Type | Required | Description |
|-------|------|----------|-------------|
| item_key | string | Yes | Key of the FAQ item |
| image | file | Yes | Image file to upload |
| order | integer | No | Display order (default: last order + 1) |

### Example Request
```bash
curl -X POST "http://your-domain.com/store/faq/images/?store_reference=store123" \
  -H "Authorization: Bearer your-auth-token" \
  -F "item_key=shipping_options_12345" \
  -F "image=@/path/to/image.jpg"
```

### Success Response (201 Created)
```json
{
    "message": "Image uploaded successfully",
    "image_data": {
        "id": 2,
        "image": "http://***********:8000/media/faq_images/wp14694522-arcane-season-2-wallpapers_gqhH1VD.jpg",
        "order": 2
    }
}
```

## 3. Delete FAQ Item Image
**Endpoint**: `DELETE /images/`  
**Description**: Deletes a specific FAQ item image.

### Query Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| image_id | integer | Yes | ID of the image to delete |
| store_reference | string | Yes | Reference ID of the store |

### Example Request
```bash
curl -X DELETE "http://your-domain.com/store/faq/images/?image_id=1&store_reference=store123" \
  -H "Authorization: Bearer your-auth-token"
```

### Success Response (200 OK)
```json
{
    "message": "Image deleted successfully"
}
```

## 4. Reorder FAQ Categories
**Endpoint**: `POST /categories/reorder/`  
**Description**: Changes the order of a category.

### Query Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| store_reference | string | Yes | Reference ID of the store |

### Request Body
```json
{
    "category_key": "general",
    "new_order": 2
  }
```

### Example Request
```bash
curl -X POST "http://your-domain.com/store/faq/categories/reorder/?store_reference=store123" \
  -H "Authorization: Bearer your-auth-token" \
  -H "Content-Type: application/json" \
  -d '{"category_key": "shipping", "new_order": 2}'
```

### Success Response (200 OK)
```json
{
  "message": "Category order updated successfully"
}
```

## 5. Switch FAQ Categories
**Endpoint**: `POST /categories/switch/`  
**Description**: Swaps the positions of two categories.

### Query Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| store_reference | string | Yes | Reference ID of the store |

### Request Body
```json
{
  "category_key_1": "shipping",
  "category_key_2": "returns"
}
```

### Example Request
```bash
curl -X POST "http://your-domain.com/store/faq/categories/switch/?store_reference=store123" \
  -H "Authorization: Bearer your-auth-token" \
  -H "Content-Type: application/json" \
  -d '{"category_key_1": "shipping", "category_key_2": "returns"}'
```

### Success Response (200 OK)
```json
{
    "message": "Successfully switched positions of 'general' and 'general4'",
    "category_1": {
        "key": "general",
        "new_order": 5
    },
    "category_2": {
        "key": "general4",
        "new_order": 2
    }
}
```

## 6. Create FAQ Item
**Endpoint**: `POST /items/create/`  
**Description**: Creates a new FAQ item with automatic category handling.

### Request Body
```json
{
  "store_reference": "store123",
  "category_name": "Shipping",
  "question": "What are your shipping options?",
  "answer": "We offer standard and express shipping.",
  "is_active": true
}
```

### Example Request
```bash
curl -X POST "http://your-domain.com/store/faq/items/create/" \
  -H "Authorization: Bearer your-auth-token" \
  -H "Content-Type: application/json" \
  -d '{
    "store_reference": "store123",
    "category_name": "Shipping",
    "question": "What are your shipping options?",
    "answer": "We offer standard and express shipping."
    "is_active": true

  }'
```

### Success Response (201 Created)
```json
{
    "status": "success",
    "message": "FAQ item created successfully",
    "data": {
        "item_key": "What_are_your_42725",
        "question": "What are your shipping options?",
        "answer": "We offer standard and express shipping options.",
        "order": 5,
        "item_images": [],
        "store_reference": "S1744452089786"
    }
}
```

## 7. Reorder FAQ Items
**Endpoint**: `POST /items/reorder/`  
**Description**: Changes the order of an item within its category.

### Query Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| store_reference | string | Yes | Reference ID of the store |

### Request Body
```json
{
  "item_key": "shipping_options_12345",
  "new_order": 2
}
```

### Example Request
```bash
curl -X POST "http://your-domain.com/store/faq/items/reorder/?store_reference=store123" \
  -H "Authorization: Bearer your-auth-token" \
  -H "Content-Type: application/json" \
  -d '{"item_key": "shipping_options_12345", "new_order": 2}'
```

### Success Response (200 OK)
```json
{
    "message": "Item 'What_are_your_42725' moved from position 5 to 1 in category 'General'",
    "item_key": "What_are_your_42725",
    "category_key": "general",
    "old_order": 5,
    "new_order": 1
}
```

## 8. Switch FAQ Items
**Endpoint**: `POST /items/switch/`  
**Description**: Swaps the positions of two items within the same category.

### Query Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| store_reference | string | Yes | Reference ID of the store |

### Request Body
```json
{
  "item_key_1": "shipping_options_12345",
  "item_key_2": "tracking_info_67890"
}
```

### Example Request
```bash
curl -X POST "http://your-domain.com/store/faq/items/switch/?store_reference=store123" \
  -H "Authorization: Bearer your-auth-token" \
  -H "Content-Type: application/json" \
  -d '{"item_key_1": "shipping_options_12345", "item_key_2": "tracking_info_67890"}'
```

### Success Response (200 OK)
```json
{
    "message": "Successfully switched positions of 'What_are_your_68738' and 'What_are_your_55081' in category 'General'",
    "category_key": "general",
    "item_1": {
        "key": "What_are_your_68738",
        "new_order": 2
    },
    "item_2": {
        "key": "What_are_your_55081",
        "new_order": 5
    }
}
```

## Error Responses

### 400 Bad Request
```json
{
  "status": "error",
  "message": "Invalid data",
  "errors": {
    "field_name": ["Error message"]
  }
}
```

### 401 Unauthorized
```json
{
  "detail": "Authentication credentials were not provided."
}
```

### 404 Not Found
```json
{
  "error": "Resource not found"
}
```

### 500 Internal Server Error
```json
{
  "error": "Internal server error",
  "details": "Error details"
}
```

## Notes
- All endpoints require authentication
- `store_reference` is required for most operations
- Image uploads accept common image formats (JPG, PNG, etc.)
- The API follows RESTful conventions
